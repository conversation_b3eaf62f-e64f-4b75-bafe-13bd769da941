from __future__ import annotations
from typing import TYPE_CHECKING, List, Dict
from datetime import datetime

if TYPE_CHECKING:
    from main_window import MainWindow

from PySide6.QtCore import Slot, QTimer
from PySide6.QtWidgets import QLineEdit

from front.core.signals import signals
from service.third.pcs.PCS import pcs
from config import global_store
from front.core.const import IP_BASE_STR, PORT_BASE_STR, SLOT_BASE_STR
from front.core.test_thread import MainTestThread
from front.core.time_format import format_timedelta
from service.common.mes_strategy import make_mes_feature_handler
from service.common.my_logger import logger
from front.core.module_selector import ModuleSelector
from front.views.module_selection_dialog import ModuleSelectionDialog


class MainController:
    def __init__(self, view: MainWindow):
        self.view = view  # 绑定 View（MainWindow）

    @Slot()
    def handle_log_changed_sig(self, text: str):
        self.view.append_log_text(text)

    @Slot()
    def on_mes_login_logout(self):
        """
        登录和登出是同一个按钮
        登录成功和登录失败信息在下面有显示
        """
        if self.view.pushButton_login.text() == "登录":
            res = pcs.login(
                self.view.lineEdit_user.text(),
                self.view.lineEdit_password.text(),
                self.view.lineEdit_work_position_number.text(),
            )
            if res[0] == 0:
                signals.mes_info_signal.emit(res[1], "green")
                self.view.pushButton_login.setText("登出")
            else:
                signals.mes_info_signal.emit(res[1], "red")

        else:
            pcs.logout()
            self.view.lineEdit_user.setText("")
            self.view.lineEdit_password.setText("")
            self.view.lineEdit_work_position_number.setText("")
            signals.mes_info_signal.emit("请登录！", "red")
            self.view.pushButton_login.setText("登录")

    @Slot()
    def on_change_mes_info(self, text: str, color: str):
        self.view.label_login_info.setText(text)
        self.view.label_login_info.setStyleSheet(
            f"background-color: yellow; color:{color};"
        )

    @Slot()
    def add_one_empty_config(self):
        self.view.add_connection_config_one()

    @Slot()
    def show_info_message(self, title: str, info: str):
        self.view.show_info_message(title, info)

    @Slot()
    def save_connection_config(self):
        # new_config_list = self.get_connection_config_list()
        # global_store.set_config("config_list", new_config_list)
        single_loop_time = self.view.spinBox_single_loop_time.value()
        global_store.set_config("single_loop_time", single_loop_time)
        global_store.save_whole_config(global_store.get_whole_config())
        self.view.show_info_message("信息", "保存配置成功！")

    # def get_connection_config_list(self):
    #     config_list = []
    #     current_config_index: int = global_store.get_context("connection_config_index")
    #     for config_index in range(current_config_index + 1):
    #         ip_obj_name = f"{IP_BASE_STR}{config_index}"
    #         port_obj_name = f"{PORT_BASE_STR}{config_index}"
    #         ip_obj: QLineEdit = getattr(self.view, ip_obj_name)
    #         ip = ip_obj.text()
    #         port_obj: QLineEdit = getattr(self.view, port_obj_name)
    #         port = port_obj.text()
    #         config_list.append({"ip": ip, "port": port})
    #     return config_list

    @Slot()
    def start_test(self):
        logger.info("开始测试")
        connect_to_mes: int = global_store.get_config("mes")
        mes_feature_handler = make_mes_feature_handler(connect_to_mes)
        global_store.set_context("mes_feature_handler", mes_feature_handler)
        mes_str = mes_feature_handler.start_test(self.view)
        if mes_str == "login first":
            return

        # 获取原始配置数据
        oct_ip = global_store.get_context("oct_ip")
        oct_ssh_port = global_store.get_context("oct_ssh_port")
        oct_npb_config_list = global_store.get_context("oct_npb_config_list")

        # 创建模块选择器
        module_selector = ModuleSelector(oct_npb_config_list)
        module_data = module_selector.get_module_data()

        # 如果没有模块数据，直接返回
        if not module_data:
            self.show_info_message("错误", "未找到可用的模块配置数据！")
            return

        # 显示模块选择对话框
        dialog = ModuleSelectionDialog(module_data, self.view)
        if dialog.exec() != ModuleSelectionDialog.Accepted:
            logger.info("用户取消了模块选择")
            return

        # 获取用户选择的模块
        selected_modules = dialog.get_selected_modules()
        if not selected_modules:
            self.show_info_message("错误", "至少需要选择一个模块进行挂机测试！")
            return

        # 更新模块选择器的选择状态
        module_selector.update_selection(selected_modules)

        # 获取过滤后的配置列表
        filtered_config_list = module_selector.get_filtered_config_list()

        # 更新global_store中的配置，以便其他地方可以使用过滤后的配置
        global_store.set_context("filtered_oct_npb_config_list", filtered_config_list)
        global_store.set_context("selected_modules", selected_modules)

        # 记录选择信息
        summary = module_selector.get_summary_text()
        logger.info(f"模块选择完成: {summary}")
        logger.info(f"过滤后的配置数量: {len(filtered_config_list)}/{len(oct_npb_config_list)}")

        single_loop_time = self.view.spinBox_single_loop_time.value()
        self.view.pushButton_start.setEnabled(False)  # 禁用按钮，防止重复点击
        self.view.table_view_monitor_model.reinit()
        # 设置QTimer每秒更新已挂机时长
        self.test_start_time = datetime.now()
        self.timer = QTimer(self.view)
        self.timer.setInterval(1000)
        self.timer.timeout.connect(self.update_test_last_time)  # 定时器触发更新标签
        self.timer.start()

        # 使用过滤后的配置列表启动测试线程
        self.thread = MainTestThread(
            oct_ip,
            oct_ssh_port,
            filtered_config_list,  # 使用过滤后的配置
            single_loop_time,
            self.test_start_time,
        )
        self.thread.finished.connect(self.task_finished)  # 任务完成后恢复按钮
        self.thread.start()  # 启动线程

    @Slot()
    def task_finished(self, show_msg="测试结束！"):
        self.view.pushButton_start.setEnabled(True)
        self.view.pushButton_finish.setEnabled(True)
        self.view.pushButton_stop.setEnabled(True)
        self.timer.stop()
        global_store.set_context("test_running", False)
        self.show_info_message("信息", show_msg)
        logger.info("测试结束!")

    @Slot()
    def stop_test(self):
        self.view.pushButton_stop.setEnabled(False)
        if self.thread:
            self.thread.terminate()
            self.thread.wait()
            self.thread = None
            logger.info("测试中断")
        else:
            self.show_info_message("信息", "当前没有正在进行的测试")

    @Slot()
    def finish_test(self):
        self.view.pushButton_finish.setEnabled(False)
        global_store.set_context("test_running", False)
        logger.info("等待完成测试")
        self.show_info_message("信息", "请耐心等待,根据目前所处的阶段不同整个完成过程可能会持续几分钟")

    @Slot()
    def update_test_last_time(self):
        time_now = datetime.now()
        test_last_time = time_now - self.test_start_time
        label_str = format_timedelta(test_last_time)
        self.view.label_test_last_time.setText(label_str)

    @Slot()
    def create_monitor_table(self, module_list: List[Dict]):
        self.view.table_view_monitor_model.update_all_data(module_list)

    @Slot()
    def update_monitor_table_single_row(self, unique_key: tuple, update_dict: dict):
        self.view.table_view_monitor_model.update_single_row(unique_key, update_dict)

    @Slot()
    def create_monitor_table_single_row(self, new_dict):
        self.view.table_view_monitor_model.create_single_row(new_dict)

    @Slot()
    def change_row_color(self, unique_key: tuple):
        self.view.table_view_monitor_model.change_row_color(unique_key)
