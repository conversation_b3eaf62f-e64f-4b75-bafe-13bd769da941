# 模块选择功能说明

## 功能概述

新增的模块选择功能允许用户在开始挂机测试前，选择需要测试的特定模块。这样可以避免对所有模块进行测试，提高测试效率并节省资源。

## 功能特性

### 1. 智能模块识别
- 自动从OCT Excel文件中读取模块配置
- 按模块slot分组NPB流量端口
- 显示每个模块对应的所有NPB端口信息

### 2. 用户友好的选择界面
- 清晰的模块列表展示
- 每个模块显示对应的NPB端口详情
- 支持全选/全不选快捷操作
- 默认全选所有模块

### 3. 灵活的选择控制
- 用户可以选择/取消选择任意模块
- 至少需要选择一个模块（不能全部取消）
- 实时显示选择状态和摘要信息

### 4. 配置过滤和更新
- 根据用户选择自动过滤配置数据
- 只对选中的模块进行挂机测试
- 更新内存中的配置对象

## 使用流程

### 1. 启动应用
正常启动OCT-MCM O2挂机上位机应用程序。

### 2. 点击开始挂机
点击"开始挂机"按钮后，系统会：
- 读取OCT Excel文件配置
- 分析可用的模块
- 弹出模块选择对话框

### 3. 选择模块
在模块选择对话框中：
- 查看所有可用模块及其NPB端口
- 选择或取消选择需要测试的模块
- 使用"全选"/"全不选"按钮快速操作
- 确认选择并开始测试

### 4. 开始测试
确认选择后，系统将：
- 只对选中的模块进行挂机测试
- 只采集选中模块的数据
- 忽略未选中的模块

## 技术实现

### 核心组件

1. **ModuleSelector** (`front/core/module_selector.py`)
   - 处理模块数据提取和过滤
   - 管理用户选择状态
   - 生成过滤后的配置列表

2. **ModuleSelectionDialog** (`front/views/module_selection_dialog.py`)
   - 模块选择对话框UI
   - 用户交互处理
   - 选择状态管理

3. **MainController** (`front/controllers/main_controller.py`)
   - 集成模块选择功能到主流程
   - 处理用户选择结果
   - 更新配置数据

### 数据流程

```
OCT Excel文件 → OctNpbMapper → ModuleSelector → ModuleSelectionDialog
                                      ↓
用户选择 → 过滤配置 → MainTestThread → main_test → 只测试选中模块
```

## 配置示例

### 原始配置（8个模块，32个配置）
```
模块1: 192.168.2.101:CE2, CE3, CE4, CE5
模块2: 192.168.2.101:CE6, CE7, CE8, CE9
模块3: 192.168.2.101:CE10, CE11, CE12, CE13
模块4: 192.168.2.101:CE14, CE15, CE16, CE17
模块5: 192.168.2.101:CE18, CE19, CE20, CE21
模块6: 192.168.2.101:CE22, CE23, CE24, CE25
模块7: 192.168.2.101:CE26, CE27, CE28, CE29
模块8: 192.168.2.101:CE30, CE31, 192.168.2.102:CE2, CE3
```

### 用户选择模块1,2,3,4后的过滤配置（4个模块，16个配置）
```
模块1: 192.168.2.101:CE2, CE3, CE4, CE5
模块2: 192.168.2.101:CE6, CE7, CE8, CE9
模块3: 192.168.2.101:CE10, CE11, CE12, CE13
模块4: 192.168.2.101:CE14, CE15, CE16, CE17
```

## 注意事项

1. **至少选择一个模块**：系统要求至少选择一个模块进行测试，不允许全部取消选择。

2. **配置完整性**：过滤后的配置保持原有的数据结构，确保测试流程正常运行。

3. **日志记录**：系统会记录用户的选择信息和过滤结果，便于调试和追踪。

4. **内存管理**：过滤后的配置会更新到global_store中，供其他组件使用。

## 兼容性

- 与现有的挂机测试流程完全兼容
- 不影响原有的Excel文件格式
- 保持原有的UI界面布局
- 向后兼容所有现有功能
