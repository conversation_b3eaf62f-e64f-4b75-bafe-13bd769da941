# -*- coding: utf-8 -*-

from PySide6.QtCore import Qt
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
    QCheckBox, QScrollArea, QWidget, QFrame, QMessageBox
)
from PySide6.QtGui import QFont


class ModuleSelectionDialog(QDialog):
    """模块选择对话框"""
    
    def __init__(self, module_data, parent=None):
        super().__init__(parent)
        self.module_data = module_data  # 模块数据字典 {slot: {npb_ports: [...], selected: True}}
        self.selected_modules = {}  # 用户选择的模块
        self.init_ui()
        self.setup_connections()
        
    def init_ui(self):
        """初始化UI界面"""
        self.setWindowTitle("选择挂机模块")
        self.setModal(True)
        self.resize(600, 500)
        
        # 主布局
        main_layout = QVBoxLayout(self)
        
        # 标题
        title_label = QLabel("请选择需要挂机测试的模块：")
        title_font = QFont()
        title_font.setPointSize(12)
        title_font.setBold(True)
        title_label.setFont(title_font)
        main_layout.addWidget(title_label)
        
        # 说明文字
        info_label = QLabel("每个模块对应多个NPB流量端口，默认全选，至少需要选择一个模块")
        info_label.setStyleSheet("color: #666; margin-bottom: 10px;")
        main_layout.addWidget(info_label)
        
        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.Box)
        
        # 模块列表容器
        modules_widget = QWidget()
        modules_layout = QVBoxLayout(modules_widget)
        
        # 全选/全不选按钮
        select_all_layout = QHBoxLayout()
        self.select_all_btn = QPushButton("全选")
        self.deselect_all_btn = QPushButton("全不选")
        select_all_layout.addWidget(self.select_all_btn)
        select_all_layout.addWidget(self.deselect_all_btn)
        select_all_layout.addStretch()
        modules_layout.addLayout(select_all_layout)
        
        # 创建模块复选框
        self.module_checkboxes = {}
        for slot in sorted(self.module_data.keys()):
            module_info = self.module_data[slot]
            npb_ports = module_info.get('npb_ports', [])
            is_selected = module_info.get('selected', True)
            
            # 模块框架
            module_frame = QFrame()
            module_frame.setFrameShape(QFrame.Box)
            module_frame.setStyleSheet("QFrame { border: 1px solid #ccc; margin: 2px; padding: 5px; }")
            
            module_layout = QVBoxLayout(module_frame)
            
            # 模块复选框
            checkbox = QCheckBox(f"模块 {slot}")
            checkbox.setChecked(is_selected)
            checkbox_font = QFont()
            checkbox_font.setPointSize(10)
            checkbox_font.setBold(True)
            checkbox.setFont(checkbox_font)
            
            self.module_checkboxes[slot] = checkbox
            module_layout.addWidget(checkbox)
            
            # NPB端口信息
            ports_text = "NPB端口: " + ", ".join(npb_ports)
            ports_label = QLabel(ports_text)
            ports_label.setStyleSheet("color: #555; margin-left: 20px; font-size: 9pt;")
            module_layout.addWidget(ports_label)
            
            modules_layout.addWidget(module_frame)
            
            # 初始化选择状态
            self.selected_modules[slot] = is_selected
        
        scroll_area.setWidget(modules_widget)
        main_layout.addWidget(scroll_area)
        
        # 按钮区域
        button_layout = QHBoxLayout()
        button_layout.addStretch()
        
        self.confirm_btn = QPushButton("确认开始挂机")
        self.confirm_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 8px 16px;
                font-size: 10pt;
                font-weight: bold;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        
        self.cancel_btn = QPushButton("取消")
        self.cancel_btn.setStyleSheet("""
            QPushButton {
                background-color: #f44336;
                color: white;
                border: none;
                padding: 8px 16px;
                font-size: 10pt;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #da190b;
            }
        """)
        
        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.confirm_btn)
        main_layout.addLayout(button_layout)
        
    def setup_connections(self):
        """设置信号连接"""
        # 按钮连接
        self.confirm_btn.clicked.connect(self.confirm_selection)
        self.cancel_btn.clicked.connect(self.reject)
        self.select_all_btn.clicked.connect(self.select_all)
        self.deselect_all_btn.clicked.connect(self.deselect_all)
        
        # 复选框连接
        for slot, checkbox in self.module_checkboxes.items():
            checkbox.stateChanged.connect(lambda state, s=slot: self.on_module_selection_changed(s, state))
    
    def on_module_selection_changed(self, slot, state):
        """模块选择状态改变"""
        self.selected_modules[slot] = state == Qt.Checked
        
    def select_all(self):
        """全选"""
        for checkbox in self.module_checkboxes.values():
            checkbox.setChecked(True)
            
    def deselect_all(self):
        """全不选"""
        for checkbox in self.module_checkboxes.values():
            checkbox.setChecked(False)
    
    def confirm_selection(self):
        """确认选择"""
        # 检查是否至少选择了一个模块
        selected_count = sum(1 for selected in self.selected_modules.values() if selected)
        if selected_count == 0:
            QMessageBox.warning(self, "警告", "至少需要选择一个模块进行挂机测试！")
            return
            
        self.accept()
    
    def get_selected_modules(self):
        """获取选中的模块列表"""
        return [slot for slot, selected in self.selected_modules.items() if selected]
