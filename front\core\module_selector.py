# -*- coding: utf-8 -*-

from collections import defaultdict
from typing import Dict, List, Tuple


class ModuleSelector:
    """模块选择器，处理模块数据和用户选择"""
    
    def __init__(self, oct_npb_config_list: List[dict]):
        """
        初始化模块选择器
        
        Args:
            oct_npb_config_list: OCT-NPB配置列表，每个元素包含oct_slot, npb_ip, npb_data_port等字段
        """
        self.oct_npb_config_list = oct_npb_config_list
        self.module_data = self._extract_module_data()
    
    def _extract_module_data(self) -> Dict[int, Dict]:
        """
        从配置列表中提取模块数据
        
        Returns:
            模块数据字典 {slot: {npb_ports: [port1, port2, ...], selected: True}}
        """
        module_data = defaultdict(lambda: {"npb_ports": [], "selected": True})
        
        for config in self.oct_npb_config_list:
            slot = config.get("oct_slot")
            npb_ip = config.get("npb_ip")
            npb_data_port = config.get("npb_data_port")
            
            if slot is not None and npb_ip and npb_data_port:
                # 组合NPB信息为更清晰的显示格式
                npb_port_info = f"{npb_ip}:{npb_data_port}"
                if npb_port_info not in module_data[slot]["npb_ports"]:
                    module_data[slot]["npb_ports"].append(npb_port_info)
        
        return dict(module_data)
    
    def get_module_data(self) -> Dict[int, Dict]:
        """获取模块数据"""
        return self.module_data
    
    def update_selection(self, selected_modules: List[int]):
        """
        更新模块选择状态
        
        Args:
            selected_modules: 用户选择的模块slot列表
        """
        for slot in self.module_data:
            self.module_data[slot]["selected"] = slot in selected_modules
    
    def get_filtered_config_list(self) -> List[dict]:
        """
        根据用户选择过滤配置列表
        
        Returns:
            过滤后的配置列表，只包含选中模块的配置
        """
        selected_slots = {slot for slot, data in self.module_data.items() if data["selected"]}
        
        filtered_config = []
        for config in self.oct_npb_config_list:
            if config.get("oct_slot") in selected_slots:
                filtered_config.append(config)
        
        return filtered_config
    
    def get_selected_modules_info(self) -> List[Tuple[int, List[str]]]:
        """
        获取选中模块的信息
        
        Returns:
            选中模块信息列表 [(slot, [npb_port1, npb_port2, ...]), ...]
        """
        selected_info = []
        for slot, data in self.module_data.items():
            if data["selected"]:
                selected_info.append((slot, data["npb_ports"]))
        
        return sorted(selected_info, key=lambda x: x[0])
    
    def get_module_count(self) -> Tuple[int, int]:
        """
        获取模块数量信息
        
        Returns:
            (总模块数, 选中模块数)
        """
        total_count = len(self.module_data)
        selected_count = sum(1 for data in self.module_data.values() if data["selected"])
        return total_count, selected_count
    
    def has_any_selected(self) -> bool:
        """检查是否有任何模块被选中"""
        return any(data["selected"] for data in self.module_data.values())
    
    def select_all(self):
        """选择所有模块"""
        for data in self.module_data.values():
            data["selected"] = True
    
    def deselect_all(self):
        """取消选择所有模块"""
        for data in self.module_data.values():
            data["selected"] = False
    
    def get_summary_text(self) -> str:
        """
        获取选择摘要文本
        
        Returns:
            选择摘要字符串
        """
        total_count, selected_count = self.get_module_count()
        selected_info = self.get_selected_modules_info()
        
        if selected_count == 0:
            return "未选择任何模块"
        elif selected_count == total_count:
            return f"已选择全部 {total_count} 个模块"
        else:
            selected_slots = [str(slot) for slot, _ in selected_info]
            return f"已选择 {selected_count}/{total_count} 个模块: {', '.join(selected_slots)}"
